{"cells": [{"cell_type": "code", "execution_count": 1, "id": "306a0ef2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from openjij import SASampler\n", "import traci\n", "\n", "from functions import *"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["num_instances=10\n", "\n", "Tall = 400\n", "interval=20\n", "alpha, gamma = 1, 10\n", "yellow_signal_time, lam = 2, 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["result= {}"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 元データ\n", "original_net_path = 'dataset/data.net.xml'\n", "original_sumocfg_path = 'dataset/data.sumocfg'\n", "# 最適化用に一部書き換えたデータ\n", "new_net_path = 'dataset/modified_data.net.xml'\n", "new_sumocfg_path = 'dataset/modified_data.sumocfg'"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# 最適なパラメータの取得\n", "import pickle\n", "\n", "# データを開く\n", "with open(\"best_params.pickle\", \"rb\") as f:\n", "    best_params = pickle.load(f)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def extract_params(best_params):\n", "    R_params = {eval(k[2:]): v for k, v in best_params.items() if k.startswith('R_')}\n", "    beta = best_params.get('beta', None)\n", "    return R_params, beta\n", "\n", "R_params, beta = extract_params(best_params)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Retrying in 1 seconds\n", "***Starting server on port 50080 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (192ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Traffic light 1250050006 has 2 phases.\n", "Traffic light 1250050012 has 2 phases.\n", "Traffic light 1250050025 has 2 phases.\n", "Traffic light 1250050032 has 2 phases.\n", "Traffic light 1251057179 has 2 phases.\n", "Traffic light 1251057265 has 2 phases.\n", "Traffic light 1251057283 has 2 phases.\n", "Traffic light 1251057285 has 2 phases.\n", "Traffic light 1251057371 has 3 phases.\n", "Traffic light 1252304871 has 2 phases.\n", "Traffic light 1252304873 has 3 phases.\n", "Traffic light 1252304874 has 2 phases.\n", "Traffic light 1252304875 has 2 phases.\n", "Traffic light 1252304882 has 2 phases.\n", "Traffic light 1252304891 has 2 phases.\n", "Traffic light 1252304895 has 2 phases.\n", "Traffic light 1252304897 has 2 phases.\n", "Traffic light 1252304899 has 2 phases.\n", "Traffic light 1252304907 has 2 phases.\n", "Traffic light 1252304911 has 2 phases.\n", "Traffic light 1252304915 has 2 phases.\n", "Traffic light 1252304922 has 2 phases.\n", "Traffic light 1252304938 has 2 phases.\n", "Traffic light 1252304949 has 2 phases.\n", "Traffic light 1252304959 has 2 phases.\n", "Traffic light 1252304965 has 2 phases.\n", "Traffic light 1252304967 has 2 phases.\n", "Traffic light 1252304970 has 2 phases.\n", "Traffic light 1252304981 has 2 phases.\n", "Traffic light 1252304990 has 2 phases.\n", "Traffic light 1252305011 has 2 phases.\n", "Traffic light 1252305023 has 2 phases.\n", "Traffic light 1252305028 has 2 phases.\n", "Traffic light 1252305033 has 2 phases.\n", "Traffic light 1252305041 has 2 phases.\n", "Traffic light 1255299337 has 2 phases.\n", "Traffic light 1255299684 has 2 phases.\n", "Traffic light 1255299696 has 2 phases.\n", "Traffic light 2344118171 has 2 phases.\n", "Traffic light cluster_1182164819_8871916627 has 2 phases.\n", "Traffic light cluster_1207332063_8871916614 has 2 phases.\n", "Traffic light cluster_1207332103_8871916612 has 2 phases.\n", "Traffic light cluster_1250019406_8871916604 has 3 phases.\n", "Traffic light cluster_1250019446_8871916615 has 3 phases.\n", "Traffic light cluster_1252304956_2344118168 has 2 phases.\n", "Traffic light cluster_1252304979_2344118162 has 2 phases.\n", "Traffic light cluster_1252305005_2344118166 has 2 phases.\n", "Traffic light cluster_276095569_8871916613 has 2 phases.\n", "Simulation ended at time: 0.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 0.02s\n", " TraCI-Duration: 0.00s\n", " Real time factor: 0\n", " UPS: 0.000000\n", "Vehicles: \n", " Inserted: 0 (Loaded: 2)\n", " Running: 0\n", " Waiting: 0\n", "Statistics (avg of 0):\n", " RouteLength: 0.00\n", " Speed: 0.00\n", " Duration: 0.00\n", " WaitingTime: 0.00\n", " TimeLoss: 0.00\n", " DepartDelay: 0.00\n", " DepartDelayWaiting: 0.00\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Desktop/TrafficSignalOptimization/aomori_miniのコピー4/functions.py:537: UserWarning: Call to deprecated function getCompleteRedYellowGreenDefinition, use getAllProgramLogics instead.\n", "  logic = traci.trafficlight.getCompleteRedYellowGreenDefinition(tl)\n"]}], "source": ["junc_info, edge_info = get_info(new_net_path, new_sumocfg_path)\n", "lanes_from_state = extract_lanes_from_state(new_net_path)\n", "\n", "B = make_B(edge_info)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["信号付き交差点の数: 48\n", "変数の数: 100\n", "Rのキーの数: 168\n"]}], "source": ["print('信号付き交差点の数:', len(junc_info.keys()))\n", "\n", "total_var=0\n", "for junc, info in junc_info.items():\n", "    total_var += info['num_modes']\n", "print('変数の数:', total_var)\n", "print('Rのキーの数:', len(R_params.keys()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>シミュレーション</h3>"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def run_simulation(\n", "    B=None,\n", "    R=None,\n", "    alpha=None, beta=None, gamma=None,\n", "    num_reads=None,\n", "    Tall=None,\n", "    interval=None,\n", "    yellow_signal_time=None,\n", "    lam=None,\n", "    sampler=None,\n", "    method=None,\n", "    net_path=None,\n", "    sumocfg_path=None,\n", "    lanes_from_state=None,\n", "    junc_info=None,\n", "    is_visualize=False\n", "    ):\n", "\n", "    if method != 'fixed':\n", "        BR = make_normalized_BR(B, R)\n", "\n", "    if is_visualize:\n", "        sumoBinary = \"PATH_YOUR_SUMO-GUI\"\n", "    else:\n", "        sumoBinary = \"PATH_YOUR_SUMO\"\n", "    sumoCmd = [sumoBinary, \"-c\", sumocfg_path, \"--no-warnings\", \"--log\", \"sumo.log\"]\n", "    traci.start(sumoCmd)\n", "\n", "    step = -1\n", "    yellow_junc = {}\n", "    mode_log={}\n", "    execution_time_log={} # [sec]\n", "    num_car_on_map_log = {}\n", "    vehicle_counts_log = {}\n", "    while step < Tall:\n", "        if step == -1:\n", "            traci.simulationStep()\n", "            step += 1\n", "            continue\n", "\n", "        vehicle_counts = count_vehicles(lanes_from_state, 1, net_path) # 右折に重み付けしない\n", "        vehicle_counts_log[step] = vehicle_counts\n", "\n", "        vehicle_id_list = traci.vehicle.getIDList()\n", "        vehicle_count = len(vehicle_id_list)\n", "        num_car_on_map_log[step] = vehicle_count\n", "        \n", "        # 信号切替時に黄色を挟む\n", "        for junc, info in yellow_junc.items():\n", "            if info['time'] == 0:\n", "                traci.trafficlight.setRedYellowGreenState(junc, info['next_state'])\n", "                yellow_junc[junc]['time'] -= 1\n", "            if info['time'] > 0:\n", "                yellow_junc[junc]['time'] -= 1\n", "\n", "        if method == 'fixed':\n", "            mode_log[step] = {}\n", "            for junc in junc_info.keys():\n", "                current_phase = traci.trafficlight.getPhase(junc)\n", "                mode_log[step][junc] = current_phase\n", "\n", "        if step % interval == 0 and method != 'fixed':\n", "            # 最適化計算\n", "            if method == 'sa':\n", "                vehicle_counts = count_vehicles(lanes_from_state, lam, net_path) # 車の数をカウントする\n", "                C = make_normalized_C(vehicle_counts, junc_info)\n", "                lowest_dict, sampleset, elapsed_time = use_annealing(C, BR, alpha, beta, gamma, num_reads, sampler, junc_info)\n", "                mode_dict = get_mode(lowest_dict)\n", "                execution_time_log[step] =  elapsed_time # [s]\n", "            if method == 'gurobi':\n", "                vehicle_counts = count_vehicles(lanes_from_state, lam, net_path) # 車の数をカウントする\n", "                C = make_normalized_C(vehicle_counts, junc_info)\n", "                lowest_dict, runtime, elapsed_time = use_gurobi(C, BR, alpha, beta, gamma, junc_info)\n", "                mode_dict = get_mode(lowest_dict)\n", "                execution_time_log[step] = elapsed_time # [s]\n", "            mode_log[step] = mode_dict\n", "\n", "\n", "            # 信号の操作\n", "            for junc, next_phase in mode_dict.items():\n", "                traci.trafficlight.setProgram(junc, \"0\") # これが無いと挙動がおかしくなる\n", "                current_phase = traci.trafficlight.getPhase(junc)\n", "                current_state = traci.trafficlight.getRedYellowGreenState(junc)\n", "                traci.trafficlight.setPhase(junc, next_phase)\n", "                next_state = traci.trafficlight.getRedYellowGreenState(junc)\n", "                # 切替時に黄色を挟む\n", "                if current_phase != next_phase:\n", "                    new_state = transition_state(current_state, next_state)\n", "                    traci.trafficlight.setRedYellowGreenState(junc, new_state)\n", "                    yellow_junc[junc] = {'time':yellow_signal_time, 'next_state':next_state} # 黄色を挟む時間\n", "\n", "        traci.simulationStep()\n", "        step += 1\n", "\n", "    traci.close()\n", "    total_waiting_time = calculate_total_waiting_time('dataset/tripinfo-out.xml')\n", "\n", "    log = {'mode_log': mode_log, 'execution_time_log': execution_time_log, \\\n", "        'num_car_on_map_log': num_car_on_map_log, 'vehicle_counts_log': vehicle_counts_log}\n", "\n", "    return total_waiting_time, log"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>local(beta=0, <PERSON><PERSON><PERSON>)</h3>"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# シミュレーションパラメータを辞書にまとめる\n", "simulation_params = {\n", "    'B': B, \n", "    'R': R_params,\n", "    'alpha': alpha,\n", "    'beta': 0,\n", "    'gamma': gamma,\n", "    'Tall': Tall,\n", "    'interval': interval, # 最適化の感覚\n", "    'yellow_signal_time': yellow_signal_time, # 黄色を挟む時間\n", "    'lam': lam,  # 右折レーンの重み\n", "    'method': 'gurobi',\n", "    'net_path': new_net_path,\n", "    'sumocfg_path': new_sumocfg_path,\n", "    'lanes_from_state': lanes_from_state,\n", "    'junc_info': junc_info,\n", "    'is_visualize': <PERSON><PERSON><PERSON>\n", "}"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Retrying in 1 seconds\n", "***Starting server on port 50083 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (193ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Set parameter Username\n", "Academic license - for non-commercial use only - expires 2023-12-07\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 49.47s\n", " TraCI-Duration: 47.31s\n", " Real time factor: 8.10609\n", " UPS: 4835.796155\n", "Vehicles: \n", " Inserted: 966\n", " Running: 407\n", " Waiting: 0\n", " Teleports: 7 (<PERSON>: 2, <PERSON><PERSON>: 5)\n", " Emergency Stops: 10\n", " Emergency Braking: 13\n", "Statistics (avg of 966):\n", " RouteLength: 818.42\n", " Speed: 4.23\n", " Duration: 247.64\n", " WaitingTime: 138.13\n", " TimeLoss: 203.05\n", " DepartDelay: 1.45\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 133433.0 [s]\n", "Total waiting time: 37.06472222222222 [hour]\n"]}], "source": ["result['local'] = {}\n", "\n", "# シミュレーションを実行\n", "total_waiting_time, log = run_simulation(**simulation_params)\n", "print(f'Total waiting time: {total_waiting_time} [s]')\n", "print(f'Total waiting time: {total_waiting_time/3600} [hour]')\n", "\n", "result['local']['total_waiting_time'] = total_waiting_time\n", "result['local']['mode_log'] = log['mode_log'] # [s]\n", "result['local']['execution_time_log'] = log['execution_time_log'] # [s]\n", "result['local']['num_car_on_map_log'] = log['num_car_on_map_log']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3><PERSON><PERSON><PERSON></h3>"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# シミュレーションパラメータを辞書にまとめる\n", "simulation_params = {\n", "    'B': B, \n", "    'R': R_params,\n", "    'alpha': alpha,\n", "    'beta': beta,\n", "    'gamma': gamma,\n", "    'Tall': Tall,\n", "    'interval': interval, # 最適化の感覚\n", "    'yellow_signal_time': yellow_signal_time, # 黄色を挟む時間\n", "    'lam': lam,  # 右折レーンの重み\n", "    'method': 'gurobi',\n", "    'net_path': new_net_path,\n", "    'sumocfg_path': new_sumocfg_path,\n", "    'lanes_from_state': lanes_from_state,\n", "    'junc_info': junc_info,\n", "    'is_visualize': <PERSON><PERSON><PERSON>\n", "}"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Retrying in 1 seconds\n", "***Starting server on port 50086 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (200ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 50.00s\n", " TraCI-Duration: 47.91s\n", " Real time factor: 8.01984\n", " UPS: 4661.066779\n", "Vehicles: \n", " Inserted: 966\n", " Running: 318\n", " Waiting: 0\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 4\n", " Emergency Braking: 9\n", "Statistics (avg of 966):\n", " RouteLength: 893.16\n", " Speed: 4.50\n", " Duration: 241.26\n", " WaitingTime: 119.56\n", " TimeLoss: 193.05\n", " DepartDelay: 1.81\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 115499.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50092 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (194ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 49.82s\n", " TraCI-Duration: 47.69s\n", " Real time factor: 8.04914\n", " UPS: 4678.094703\n", "Vehicles: \n", " Inserted: 966\n", " Running: 318\n", " Waiting: 0\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 4\n", " Emergency Braking: 9\n", "Statistics (avg of 966):\n", " RouteLength: 893.16\n", " Speed: 4.50\n", " Duration: 241.26\n", " WaitingTime: 119.56\n", " TimeLoss: 193.05\n", " DepartDelay: 1.81\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 115499.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50096 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (198ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 49.80s\n", " TraCI-Duration: 47.72s\n", " Real time factor: 8.05156\n", " UPS: 4679.503654\n", "Vehicles: \n", " Inserted: 966\n", " Running: 318\n", " Waiting: 0\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 4\n", " Emergency Braking: 9\n", "Statistics (avg of 966):\n", " RouteLength: 893.16\n", " Speed: 4.50\n", " Duration: 241.26\n", " WaitingTime: 119.56\n", " TimeLoss: 193.05\n", " DepartDelay: 1.81\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 115499.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50101 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (200ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 49.80s\n", " TraCI-Duration: 47.71s\n", " Real time factor: 8.05253\n", " UPS: 4680.067473\n", "Vehicles: \n", " Inserted: 966\n", " Running: 318\n", " Waiting: 0\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 4\n", " Emergency Braking: 9\n", "Statistics (avg of 966):\n", " RouteLength: 893.16\n", " Speed: 4.50\n", " Duration: 241.26\n", " WaitingTime: 119.56\n", " TimeLoss: 193.05\n", " DepartDelay: 1.81\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 115499.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50104 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (200ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 49.85s\n", " TraCI-Duration: 47.78s\n", " Real time factor: 8.04365\n", " UPS: 4674.904218\n", "Vehicles: \n", " Inserted: 966\n", " Running: 318\n", " Waiting: 0\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 4\n", " Emergency Braking: 9\n", "Statistics (avg of 966):\n", " RouteLength: 893.16\n", " Speed: 4.50\n", " Duration: 241.26\n", " WaitingTime: 119.56\n", " TimeLoss: 193.05\n", " DepartDelay: 1.81\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 115499.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50110 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (194ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 49.93s\n", " TraCI-Duration: 47.84s\n", " Real time factor: 8.03189\n", " UPS: 4668.068742\n", "Vehicles: \n", " Inserted: 966\n", " Running: 318\n", " Waiting: 0\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 4\n", " Emergency Braking: 9\n", "Statistics (avg of 966):\n", " RouteLength: 893.16\n", " Speed: 4.50\n", " Duration: 241.26\n", " WaitingTime: 119.56\n", " TimeLoss: 193.05\n", " DepartDelay: 1.81\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 115499.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50113 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (198ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 49.99s\n", " TraCI-Duration: 47.90s\n", " Real time factor: 8.02225\n", " UPS: 4662.465490\n", "Vehicles: \n", " Inserted: 966\n", " Running: 318\n", " Waiting: 0\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 4\n", " Emergency Braking: 9\n", "Statistics (avg of 966):\n", " RouteLength: 893.16\n", " Speed: 4.50\n", " Duration: 241.26\n", " WaitingTime: 119.56\n", " TimeLoss: 193.05\n", " DepartDelay: 1.81\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 115499.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50117 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (197ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 50.03s\n", " TraCI-Duration: 47.94s\n", " Real time factor: 8.01503\n", " UPS: 4658.271871\n", "Vehicles: \n", " Inserted: 966\n", " Running: 318\n", " Waiting: 0\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 4\n", " Emergency Braking: 9\n", "Statistics (avg of 966):\n", " RouteLength: 893.16\n", " Speed: 4.50\n", " Duration: 241.26\n", " WaitingTime: 119.56\n", " TimeLoss: 193.05\n", " DepartDelay: 1.81\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 115499.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50121 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (196ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 50.12s\n", " TraCI-Duration: 48.01s\n", " Real time factor: 8.00128\n", " UPS: 4650.278349\n", "Vehicles: \n", " Inserted: 966\n", " Running: 318\n", " Waiting: 0\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 4\n", " Emergency Braking: 9\n", "Statistics (avg of 966):\n", " RouteLength: 893.16\n", " Speed: 4.50\n", " Duration: 241.26\n", " WaitingTime: 119.56\n", " TimeLoss: 193.05\n", " DepartDelay: 1.81\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 115499.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50125 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (189ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 49.80s\n", " TraCI-Duration: 47.72s\n", " Real time factor: 8.05189\n", " UPS: 4679.691579\n", "Vehicles: \n", " Inserted: 966\n", " Running: 318\n", " Waiting: 0\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 4\n", " Emergency Braking: 9\n", "Statistics (avg of 966):\n", " RouteLength: 893.16\n", " Speed: 4.50\n", " Duration: 241.26\n", " WaitingTime: 119.56\n", " TimeLoss: 193.05\n", " DepartDelay: 1.81\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 115499.0 [s]\n"]}], "source": ["total_waiting_time_log={}\n", "execution_time_log={}\n", "\n", "result['gurobi'] = {}\n", "for t in range(num_instances):\n", "    result['gurobi']['iter'+str(t)]={}\n", "    # シミュレーションを実行\n", "    total_waiting_time, log = run_simulation(**simulation_params)\n", "    print(f'Total waiting time: {total_waiting_time} [s]')\n", "\n", "    result['gurobi']['iter'+str(t)]['total_waiting_time'] = total_waiting_time\n", "    result['gurobi']['iter'+str(t)]['mode_log'] = log['mode_log'] # [s]\n", "    result['gurobi']['iter'+str(t)]['execution_time_log'] = log['execution_time_log'] # [s]\n", "    result['gurobi']['iter'+str(t)]['num_car_on_map_log'] = log['num_car_on_map_log']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>SA</h3>"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# シミュレーションパラメータを辞書にまとめる\n", "simulation_params = {\n", "    'B': B, \n", "    'R': R_params,\n", "    'alpha': alpha,\n", "    'beta': beta,\n", "    'gamma': gamma,\n", "    'num_reads': 1000,\n", "    'Tall': Tall,\n", "    'interval': interval, # 最適化の感覚\n", "    'yellow_signal_time': yellow_signal_time, # 黄色を挟む時間\n", "    'lam': lam,  # 右折レーンの重み\n", "    'sampler': <PERSON><PERSON><PERSON>(),\n", "    'method': 'sa',\n", "    'net_path': new_net_path,\n", "    'sumocfg_path': new_sumocfg_path,\n", "    'lanes_from_state': lanes_from_state,\n", "    'junc_info': junc_info,\n", "    'is_visualize': <PERSON><PERSON><PERSON>\n", "}\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Retrying in 1 seconds\n", "***Starting server on port 50139 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (197ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 94.06s\n", " TraCI-Duration: 91.92s\n", " Real time factor: 4.26342\n", " UPS: 2556.094242\n", "Vehicles: \n", " Inserted: 965 (Loaded: 966)\n", " Running: 389\n", " Waiting: 1\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 12\n", " Emergency Braking: 16\n", "Statistics (avg of 965):\n", " RouteLength: 841.93\n", " Speed: 4.22\n", " Duration: 249.13\n", " WaitingTime: 136.22\n", " TimeLoss: 203.51\n", " DepartDelay: 1.82\n", " DepartDelayWaiting: 302.40\n", "\n", "Total waiting time: 131453.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50142 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (198ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 94.07s\n", " TraCI-Duration: 91.94s\n", " Real time factor: 4.26287\n", " UPS: 2574.924523\n", "Vehicles: \n", " Inserted: 966\n", " Running: 393\n", " Waiting: 0\n", " Emergency Stops: 5\n", " Emergency Braking: 9\n", "Statistics (avg of 966):\n", " RouteLength: 824.92\n", " Speed: 4.15\n", " Duration: 250.74\n", " WaitingTime: 137.26\n", " TimeLoss: 205.89\n", " DepartDelay: 2.26\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 132596.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50147 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (196ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 94.57s\n", " TraCI-Duration: 92.43s\n", " Real time factor: 4.24047\n", " UPS: 2551.578279\n", "Vehicles: \n", " Inserted: 966\n", " Running: 375\n", " Waiting: 0\n", " Teleports: 2 (<PERSON><PERSON>: 2)\n", " Emergency Stops: 8\n", " Emergency Braking: 13\n", "Statistics (avg of 966):\n", " RouteLength: 861.89\n", " Speed: 4.29\n", " Duration: 249.78\n", " WaitingTime: 130.94\n", " TimeLoss: 203.12\n", " DepartDelay: 1.69\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 126486.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50152 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (187ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 94.04s\n", " TraCI-Duration: 91.94s\n", " Real time factor: 4.26396\n", " UPS: 2594.104887\n", "Vehicles: \n", " Inserted: 965 (Loaded: 966)\n", " Running: 402\n", " Waiting: 1\n", " Teleports: 3 (<PERSON><PERSON>: 3)\n", " Emergency Stops: 7\n", " Emergency Braking: 13\n", "Statistics (avg of 965):\n", " RouteLength: 831.43\n", " Speed: 4.10\n", " Duration: 252.81\n", " WaitingTime: 138.55\n", " TimeLoss: 207.67\n", " DepartDelay: 1.53\n", " DepartDelayWaiting: 354.10\n", "\n", "Total waiting time: 133698.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50156 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (200ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 94.22s\n", " TraCI-Duration: 92.11s\n", " Real time factor: 4.25591\n", " UPS: 2551.919934\n", "Vehicles: \n", " Inserted: 966\n", " Running: 410\n", " Waiting: 0\n", " Teleports: 7 (<PERSON>: 2, <PERSON><PERSON>: 5)\n", " Emergency Stops: 5\n", " Emergency Braking: 9\n", "Statistics (avg of 966):\n", " RouteLength: 817.21\n", " Speed: 4.16\n", " Duration: 248.91\n", " WaitingTime: 138.47\n", " TimeLoss: 204.44\n", " DepartDelay: 1.76\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 133759.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50159 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (198ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 94.16s\n", " TraCI-Duration: 92.04s\n", " Real time factor: 4.25853\n", " UPS: 2490.909477\n", "Vehicles: \n", " Inserted: 964 (Loaded: 966)\n", " Running: 348\n", " Waiting: 2\n", " Teleports: 4 (<PERSON>: 2, <PERSON><PERSON>: 2)\n", " Emergency Stops: 6\n", " Emergency Braking: 12\n", "Statistics (avg of 964):\n", " RouteLength: 844.76\n", " Speed: 4.36\n", " Duration: 243.31\n", " WaitingTime: 132.39\n", " TimeLoss: 197.47\n", " DepartDelay: 1.48\n", " DepartDelayWaiting: 318.50\n", "\n", "Total waiting time: 127626.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50162 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (200ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 94.23s\n", " TraCI-Duration: 92.14s\n", " Real time factor: 4.25559\n", " UPS: 2515.085589\n", "Vehicles: \n", " Inserted: 966\n", " Running: 382\n", " Waiting: 0\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 8\n", " Emergency Braking: 14\n", "Statistics (avg of 966):\n", " RouteLength: 848.72\n", " Speed: 4.30\n", " Duration: 245.34\n", " WaitingTime: 129.55\n", " TimeLoss: 199.48\n", " DepartDelay: 1.77\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 125144.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50166 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (197ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 94.29s\n", " TraCI-Duration: 92.17s\n", " Real time factor: 4.25297\n", " UPS: 2548.739487\n", "Vehicles: \n", " Inserted: 966\n", " Running: 397\n", " Waiting: 0\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 7\n", " Emergency Braking: 11\n", "Statistics (avg of 966):\n", " RouteLength: 843.53\n", " Speed: 4.25\n", " Duration: 248.77\n", " WaitingTime: 133.18\n", " TimeLoss: 203.01\n", " DepartDelay: 2.50\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 128654.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50169 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (197ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 94.30s\n", " TraCI-Duration: 92.19s\n", " Real time factor: 4.2523\n", " UPS: 2503.000997\n", "Vehicles: \n", " Inserted: 965 (Loaded: 966)\n", " Running: 358\n", " Waiting: 1\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 5\n", " Emergency Braking: 11\n", "Statistics (avg of 965):\n", " RouteLength: 859.55\n", " Speed: 4.40\n", " Duration: 244.60\n", " WaitingTime: 126.58\n", " TimeLoss: 198.07\n", " DepartDelay: 1.60\n", " DepartDelayWaiting: 302.40\n", "\n", "Total waiting time: 122153.0 [s]\n", " Retrying in 1 seconds\n", "***Starting server on port 50176 ***\n", "Loading net-file from 'dataset/modified_data.net.xml' ... done (199ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 94.31s\n", " TraCI-Duration: 92.20s\n", " Real time factor: 4.25216\n", " UPS: 2533.428768\n", "Vehicles: \n", " Inserted: 965 (Loaded: 966)\n", " Running: 378\n", " Waiting: 1\n", " Teleports: 1 (<PERSON><PERSON>: 1)\n", " Emergency Stops: 3\n", " Emergency Braking: 5\n", "Statistics (avg of 965):\n", " RouteLength: 839.78\n", " Speed: 4.31\n", " Duration: 247.58\n", " WaitingTime: 133.92\n", " TimeLoss: 201.98\n", " DepartDelay: 1.79\n", " DepartDelayWaiting: 302.40\n", "\n", "Total waiting time: 129233.0 [s]\n"]}], "source": ["total_waiting_time_log={}\n", "execution_time_log={}\n", "\n", "result['sa'] = {}\n", "for t in range(num_instances):\n", "    result['sa']['iter'+str(t)]={}\n", "    # シミュレーションを実行\n", "    total_waiting_time, log = run_simulation(**simulation_params)\n", "    print(f'Total waiting time: {total_waiting_time} [s]')\n", "    result['sa']['iter'+str(t)]['total_waiting_time'] = total_waiting_time\n", "    result['sa']['iter'+str(t)]['mode_log'] = log['mode_log'] # [s]\n", "    result['sa']['iter'+str(t)]['execution_time_log'] = log['execution_time_log'] # [s]\n", "    result['sa']['iter'+str(t)]['num_car_on_map_log'] = log['num_car_on_map_log'] # [s]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>固定サイクル</h3>"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# シミュレーションパラメータを辞書にまとめる\n", "simulation_params = {\n", "    'Tall': Tall,\n", "    'interval': interval,\n", "    'method': 'fixed',\n", "    'net_path': original_net_path,\n", "    'sumocfg_path': original_sumocfg_path,\n", "    'lanes_from_state': lanes_from_state,\n", "    'junc_info': junc_info,\n", "    'is_visualize': <PERSON><PERSON><PERSON>\n", "}"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Retrying in 1 seconds\n", "***Starting server on port 50186 ***\n", "Loading net-file from 'dataset/data.net.xml' ... done (192ms).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n", "pj_obj_create: /Users/<USER>/opt/anaconda3/envs/opt/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 1 whereas a number >= 2 is expected. It comes from another PROJ installation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading done.\n", "Simulation version 1.18.0 started with time: 0.00.\n", "Simulation ended at time: 401.00\n", "Reason: TraCI requested termination.\n", "Performance: \n", " Duration: 49.69s\n", " TraCI-Duration: 47.57s\n", " Real time factor: 8.07036\n", " UPS: 5083.802930\n", "Vehicles: \n", " Inserted: 966\n", " Running: 484\n", " Waiting: 0\n", " Teleports: 3 (<PERSON><PERSON>: 3)\n", "Statistics (avg of 966):\n", " RouteLength: 770.63\n", " Speed: 3.76\n", " Duration: 261.49\n", " WaitingTime: 161.62\n", " TimeLoss: 219.29\n", " DepartDelay: 1.59\n", " DepartDelayWaiting: 0.00\n", "\n", "Total waiting time: 156125.0 [s]\n"]}], "source": ["total_waiting_time_log={}\n", "execution_time_log={}\n", "\n", "result['fixed'] = {}\n", "\n", "# シミュレーションを実行\n", "total_waiting_time, log = run_simulation(**simulation_params)\n", "print(f'Total waiting time: {total_waiting_time} [s]')\n", "\n", "result['fixed']['total_waiting_time'] = total_waiting_time\n", "result['fixed']['mode_log'] = log['mode_log'] # [s]\n", "result['fixed']['execution_time_log'] = log['execution_time_log'] # [s]\n", "result['fixed']['num_car_on_map_log'] = log['num_car_on_map_log'] # [s]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>結果の保存</h3>"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "# データを保存\n", "with open(\"result.pickle\", \"wb\") as f:\n", "    pickle.dump(result, f)"]}], "metadata": {"kernelspec": {"display_name": "opt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 2}