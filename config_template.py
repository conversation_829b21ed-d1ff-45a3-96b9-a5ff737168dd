"""
Configuration template for Traffic Signal Optimization project.
Copy this file to config.py and update the paths according to your system.
"""

import os
import platform

# Detect operating system
SYSTEM = platform.system().lower()

# SUMO Configuration
# Update these paths according to your SUMO installation
if SYSTEM == "windows":
    # Windows default installation paths
    SUMO_HOME = os.environ.get('SUMO_HOME', r'C:\Program Files (x86)\Eclipse\Sumo')
    SUMO_BINARY = os.path.join(SUMO_HOME, 'bin', 'sumo.exe')
    SUMO_GUI_BINARY = os.path.join(SUMO_HOME, 'bin', 'sumo-gui.exe')
    
elif SYSTEM == "darwin":  # macOS
    # macOS default installation paths
    SUMO_HOME = os.environ.get('SUMO_HOME', '/usr/local/share/sumo')
    SUMO_BINARY = 'sumo'  # Should be in PATH
    SUMO_GUI_BINARY = 'sumo-gui'
    
else:  # Linux and others
    # Linux default installation paths
    SUMO_HOME = os.environ.get('SUMO_HOME', '/usr/share/sumo')
    SUMO_BINARY = 'sumo'  # Should be in PATH
    SUMO_GUI_BINARY = 'sumo-gui'

# Verify SUMO installation
def verify_sumo_installation():
    """Verify that SUMO is properly installed and accessible."""
    import subprocess
    try:
        result = subprocess.run([SUMO_BINARY, '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"SUMO found: {result.stdout.split()[0]}")
            return True
        else:
            print(f"SUMO binary found but returned error: {result.stderr}")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError) as e:
        print(f"SUMO binary not found: {e}")
        print(f"Tried to run: {SUMO_BINARY}")
        print("Please check your SUMO installation and update the paths in this config file.")
        return False

# Project paths
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
DATASET_DIR = os.path.join(PROJECT_ROOT, 'dataset')

# Data files
ORIGINAL_NET_PATH = os.path.join(DATASET_DIR, 'data.net.xml')
ORIGINAL_SUMOCFG_PATH = os.path.join(DATASET_DIR, 'data.sumocfg')
MODIFIED_NET_PATH = os.path.join(DATASET_DIR, 'modified_data.net.xml')
MODIFIED_SUMOCFG_PATH = os.path.join(DATASET_DIR, 'modified_data.sumocfg')

# Output files
TRIPINFO_OUTPUT = os.path.join(DATASET_DIR, 'tripinfo-out.xml')
QUEUE_OUTPUT = os.path.join(DATASET_DIR, 'queue-out.xml')
NETSTATE_OUTPUT = os.path.join(DATASET_DIR, 'netstate-out.xml')

# Results files
BEST_PARAMS_FILE = os.path.join(PROJECT_ROOT, 'best_params.pickle')
RESULTS_FILE = os.path.join(PROJECT_ROOT, 'result.pickle')

# Simulation parameters
DEFAULT_SIMULATION_PARAMS = {
    'Tall': 400,           # Total simulation time
    'interval': 20,        # Optimization interval
    'alpha': 1,            # Weight for vehicle count term
    'gamma': 10,           # Weight for constraint term
    'yellow_signal_time': 2,  # Yellow signal duration
    'lam': 2,              # Right turn lane weight
    'num_reads': 1000,     # Number of annealing reads
}

# Optimization parameters
OPTUNA_PARAMS = {
    'n_trials': 100,       # Number of optimization trials
    'timeout': 3600,       # Timeout in seconds
}

# Gurobi parameters
GUROBI_PARAMS = {
    'OutputFlag': 0,       # Suppress Gurobi output
    'TimeLimit': 60,       # Time limit in seconds
}

# Visualization parameters
PLOT_PARAMS = {
    'figsize': (12, 8),
    'dpi': 100,
    'style': 'seaborn',
}

def get_sumo_command(config_path, gui=False, additional_args=None):
    """
    Generate SUMO command with proper binary and arguments.
    
    Args:
        config_path: Path to SUMO configuration file
        gui: Whether to use GUI version
        additional_args: List of additional command line arguments
    
    Returns:
        List of command arguments for subprocess
    """
    binary = SUMO_GUI_BINARY if gui else SUMO_BINARY
    cmd = [binary, "-c", config_path, "--no-warnings", "--log", "sumo.log"]
    
    if additional_args:
        cmd.extend(additional_args)
    
    return cmd

def setup_environment():
    """Set up environment variables for SUMO."""
    if SUMO_HOME and os.path.exists(SUMO_HOME):
        os.environ['SUMO_HOME'] = SUMO_HOME
        
        # Add SUMO tools to Python path
        tools_path = os.path.join(SUMO_HOME, 'tools')
        if os.path.exists(tools_path):
            import sys
            if tools_path not in sys.path:
                sys.path.append(tools_path)
    
    return verify_sumo_installation()

# Auto-setup when imported
if __name__ == "__main__":
    print("Testing SUMO configuration...")
    if setup_environment():
        print("✓ SUMO configuration is valid")
    else:
        print("✗ SUMO configuration failed")
        print("Please update the paths in this config file according to your system.")
else:
    # Silent setup when imported
    setup_environment()
