#!/usr/bin/env python3
"""
Test script to verify that all dependencies for the Traffic Signal Optimization project are properly installed.
Run this script after following the setup guide to ensure everything is working correctly.
"""

import sys
import os

def test_core_libraries():
    """Test core scientific computing libraries."""
    print("Testing core libraries...")
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError:
        print("✗ NumPy import failed")
        return False
    
    try:
        import pandas as pd
        print(f"✓ Pandas {pd.__version__}")
    except ImportError:
        print("✗ Pandas import failed")
        return False
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib
        print(f"✓ Matplotlib {matplotlib.__version__}")
    except ImportError:
        print("✗ Matplotlib import failed")
        return False
    
    try:
        import seaborn as sns
        print(f"✓ Seaborn {sns.__version__}")
    except ImportError:
        print("✗ Seaborn import failed")
        return False
    
    return True

def test_optimization_libraries():
    """Test optimization libraries."""
    print("\nTesting optimization libraries...")
    success = True
    
    try:
        import pyqubo
        print(f"✓ PyQUBO {pyqubo.__version__}")
    except ImportError:
        print("✗ PyQUBO import failed")
        success = False
    
    try:
        import gurobipy as gp
        print(f"✓ Gurobi {gp.gurobi.version()}")
        
        # Test Gurobi license
        try:
            model = gp.Model()
            model.dispose()
            print("✓ Gurobi license is valid")
        except Exception as e:
            print(f"⚠ Gurobi license issue: {e}")
    except ImportError:
        print("✗ Gurobi import failed")
        success = False
    
    try:
        import openjij
        print(f"✓ OpenJij {openjij.__version__}")
    except ImportError:
        print("✗ OpenJij import failed")
        success = False
    
    try:
        import neal
        print("✓ Neal (D-Wave) imported successfully")
    except ImportError:
        print("✗ Neal import failed")
        success = False
    
    try:
        import optuna
        print(f"✓ Optuna {optuna.__version__}")
    except ImportError:
        print("✗ Optuna import failed")
        success = False
    
    return success

def test_sumo():
    """Test SUMO installation and TraCI."""
    print("\nTesting SUMO installation...")
    
    # Check environment variables
    sumo_home = os.environ.get('SUMO_HOME')
    if sumo_home:
        print(f"✓ SUMO_HOME is set: {sumo_home}")
    else:
        print("⚠ SUMO_HOME environment variable not set")
    
    # Test TraCI import
    try:
        import traci
        print("✓ TraCI imported successfully")
        
        # Test if SUMO binary is accessible
        import subprocess
        try:
            result = subprocess.run(['sumo', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[0]
                print(f"✓ SUMO binary accessible: {version_line}")
                return True
            else:
                print("✗ SUMO binary not accessible")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("✗ SUMO binary not found in PATH")
            return False
            
    except ImportError:
        print("✗ TraCI import failed - check SUMO installation and PYTHONPATH")
        return False

def test_jupyter():
    """Test Jupyter notebook support."""
    print("\nTesting Jupyter support...")
    
    try:
        import jupyter
        print("✓ Jupyter imported successfully")
    except ImportError:
        print("✗ Jupyter import failed")
        return False
    
    try:
        import IPython
        print(f"✓ IPython {IPython.__version__}")
    except ImportError:
        print("✗ IPython import failed")
        return False
    
    return True

def test_project_files():
    """Test if project files are present."""
    print("\nTesting project files...")
    
    required_files = [
        'functions.py',
        'main.ipynb',
        'parameter_search.ipynb',
        'preprocessing.ipynb',
        'show_result.ipynb',
        'dataset/data.net.xml',
        'dataset/data.rou.xml',
        'dataset/data.sumocfg'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file} not found")
            missing_files.append(file)
    
    return len(missing_files) == 0

def test_functions_import():
    """Test importing the project's functions module."""
    print("\nTesting project functions...")
    
    try:
        from functions import (
            extract_lanes_from_state,
            count_vehicles,
            extract_edge_info,
            calculate_total_waiting_time,
            use_annealing,
            use_gurobi
        )
        print("✓ Core functions imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Functions import failed: {e}")
        return False

def main():
    """Run all tests and provide summary."""
    print("=" * 60)
    print("Traffic Signal Optimization - Setup Verification")
    print("=" * 60)
    
    tests = [
        ("Core Libraries", test_core_libraries),
        ("Optimization Libraries", test_optimization_libraries),
        ("SUMO Installation", test_sumo),
        ("Jupyter Support", test_jupyter),
        ("Project Files", test_project_files),
        ("Project Functions", test_functions_import)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} test failed with error: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        print(f"{test_name:<25} {status}")
        if success:
            passed += 1
    
    print(f"\nTests passed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your setup is ready.")
        print("You can now run the Jupyter notebooks:")
        print("  jupyter notebook preprocessing.ipynb")
        print("  jupyter notebook main.ipynb")
    else:
        print(f"\n⚠ {len(results) - passed} test(s) failed.")
        print("Please check the setup guide and fix the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
