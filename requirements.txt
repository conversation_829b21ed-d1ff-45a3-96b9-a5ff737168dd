# Traffic Signal Optimization Project Requirements
# This file contains all the dependencies needed to run the traffic signal optimization project

# Core scientific computing libraries
numpy>=1.21.0
pandas>=1.3.0

# Visualization libraries
matplotlib>=3.4.0
seaborn>=0.11.0

# Optimization libraries
pyqubo>=1.2.0
gurobipy>=9.5.0
openjij>=0.6.0
neal>=0.5.4
optuna>=3.0.0

# Jupyter notebook support
jupyter>=1.0.0
ipython>=7.0.0
notebook>=6.4.0

# Additional dependencies that might be needed
scipy>=1.7.0
scikit-learn>=1.0.0

# SUMO Traffic Simulation
# Note: SUMO needs to be installed separately from https://sumo.dlr.de/
# The traci module comes with SUMO installation
# After installing SUMO, make sure to:
# 1. Add SUMO_HOME environment variable pointing to SUMO installation directory
# 2. Add SUMO/tools to your PYTHONPATH
# 3. Update the PATH_YOUR_SUMO variables in functions.py to point to your SUMO binary

# For pickle support (usually included with Python)
# pickle is part of Python standard library

# For XML processing (included with Python)
# xml.etree.ElementTree is part of Python standard library

# Installation instructions:
# 1. Install Python dependencies: pip install -r requirements.txt
# 2. Install SUMO from https://sumo.dlr.de/docs/Installing/index.html
# 3. Set up SUMO environment variables
# 4. Update SUMO binary paths in the code
# 5. For Gurobi, you'll need a license (academic licenses are free)
