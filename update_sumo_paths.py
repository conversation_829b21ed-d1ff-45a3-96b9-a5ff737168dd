#!/usr/bin/env python3
"""
Script to automatically update SUMO binary paths in functions.py
This script will replace the placeholder paths with actual SUMO binary paths.
"""

import os
import re
import platform
import subprocess
import shutil

def detect_sumo_binary():
    """Detect SUMO binary path automatically."""
    system = platform.system().lower()
    
    # Try common binary names
    binary_names = ['sumo', 'sumo.exe']
    gui_binary_names = ['sumo-gui', 'sumo-gui.exe']
    
    sumo_binary = None
    sumo_gui_binary = None
    
    # First, try to find in PATH
    for binary in binary_names:
        try:
            result = subprocess.run([binary, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                sumo_binary = binary
                break
        except (subprocess.TimeoutExpired, FileNotFoundError):
            continue
    
    for binary in gui_binary_names:
        try:
            result = subprocess.run([binary, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                sumo_gui_binary = binary
                break
        except (subprocess.TimeoutExpired, FileNotFoundError):
            continue
    
    # If not found in PATH, try common installation directories
    if not sumo_binary:
        common_paths = []
        
        if system == "windows":
            common_paths = [
                r'C:\Program Files (x86)\Eclipse\Sumo\bin\sumo.exe',
                r'C:\Program Files\Eclipse\Sumo\bin\sumo.exe',
                r'C:\sumo\bin\sumo.exe'
            ]
            gui_paths = [path.replace('sumo.exe', 'sumo-gui.exe') for path in common_paths]
        elif system == "darwin":  # macOS
            common_paths = [
                '/usr/local/bin/sumo',
                '/opt/homebrew/bin/sumo',
                '/usr/local/share/sumo/bin/sumo'
            ]
            gui_paths = [path.replace('sumo', 'sumo-gui') for path in common_paths]
        else:  # Linux
            common_paths = [
                '/usr/bin/sumo',
                '/usr/local/bin/sumo',
                '/opt/sumo/bin/sumo'
            ]
            gui_paths = [path.replace('sumo', 'sumo-gui') for path in common_paths]
        
        # Check common paths
        for path in common_paths:
            if os.path.exists(path):
                try:
                    result = subprocess.run([path, '--version'], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        sumo_binary = path
                        break
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    continue
        
        for path in gui_paths:
            if os.path.exists(path):
                try:
                    result = subprocess.run([path, '--version'], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        sumo_gui_binary = path
                        break
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    continue
    
    return sumo_binary, sumo_gui_binary

def update_functions_py(sumo_binary, sumo_gui_binary):
    """Update the SUMO paths in functions.py"""
    
    if not os.path.exists('functions.py'):
        print("Error: functions.py not found in current directory")
        return False
    
    # Create backup
    backup_file = 'functions.py.backup'
    shutil.copy2('functions.py', backup_file)
    print(f"Created backup: {backup_file}")
    
    # Read the file
    with open('functions.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace the placeholder paths
    # Pattern to match: sumoBinary = "PATH_YOUR_SUMO"
    pattern1 = r'sumoBinary\s*=\s*["\']PATH_YOUR_SUMO["\']'
    pattern2 = r'sumoBinary\s*=\s*["\']PATH_YOUR_SUMO-GUI["\']'
    
    if sumo_binary:
        # Use forward slashes for cross-platform compatibility
        sumo_path = sumo_binary.replace('\\', '/')
        content = re.sub(pattern1, f'sumoBinary = "{sumo_path}"', content)
        print(f"Updated SUMO binary path to: {sumo_path}")
    else:
        print("Warning: SUMO binary not found, keeping placeholder")
    
    if sumo_gui_binary:
        # Use forward slashes for cross-platform compatibility
        sumo_gui_path = sumo_gui_binary.replace('\\', '/')
        content = re.sub(pattern2, f'sumoBinary = "{sumo_gui_path}"', content)
        print(f"Updated SUMO GUI binary path to: {sumo_gui_path}")
    else:
        print("Warning: SUMO GUI binary not found, keeping placeholder")
    
    # Write the updated content
    with open('functions.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def main():
    """Main function to update SUMO paths."""
    print("Detecting SUMO installation...")
    
    sumo_binary, sumo_gui_binary = detect_sumo_binary()
    
    if sumo_binary:
        print(f"✓ Found SUMO binary: {sumo_binary}")
        
        # Test the binary
        try:
            result = subprocess.run([sumo_binary, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            version_info = result.stdout.split('\n')[0]
            print(f"  Version: {version_info}")
        except Exception as e:
            print(f"  Warning: Could not get version info: {e}")
    else:
        print("✗ SUMO binary not found")
        print("Please install SUMO and ensure it's in your PATH, or update paths manually")
    
    if sumo_gui_binary:
        print(f"✓ Found SUMO GUI binary: {sumo_gui_binary}")
    else:
        print("✗ SUMO GUI binary not found")
    
    if sumo_binary or sumo_gui_binary:
        print("\nUpdating functions.py...")
        if update_functions_py(sumo_binary, sumo_gui_binary):
            print("✓ Successfully updated functions.py")
            print("You can now run the project notebooks!")
        else:
            print("✗ Failed to update functions.py")
    else:
        print("\nNo SUMO binaries found. Please:")
        print("1. Install SUMO from https://sumo.dlr.de/")
        print("2. Add SUMO to your system PATH")
        print("3. Run this script again")
        print("4. Or manually update the paths in functions.py")

if __name__ == "__main__":
    main()
