# Traffic Signal Optimization - Setup Guide

This guide will help you set up the Traffic Signal Optimization project on your system.

## Prerequisites

- Python 3.7 or higher
- Git (for cloning the repository)
- Administrative privileges (for SUMO installation)

## Step 1: Install Python Dependencies

1. Create a virtual environment (recommended):
```bash
python -m venv traffic_optimization_env
source traffic_optimization_env/bin/activate  # On Windows: traffic_optimization_env\Scripts\activate
```

2. Install Python packages:
```bash
pip install -r requirements.txt
```

## Step 2: Install SUMO

### Windows:
1. Download SUMO from: https://sumo.dlr.de/docs/Installing/Windows_Build.html
2. Run the installer and follow the setup wizard
3. Add SUMO to your system PATH during installation

### macOS:
```bash
# Using Homebrew
brew install sumo

# Or download from: https://sumo.dlr.de/docs/Installing/MacOS_Build.html
```

### Linux (Ubuntu/Debian):
```bash
sudo apt-get update
sudo apt-get install sumo sumo-tools sumo-doc
```

## Step 3: Configure Environment Variables

Add these environment variables to your system:

### Windows:
```cmd
set SUMO_HOME=C:\Program Files (x86)\Eclipse\Sumo
set PATH=%PATH%;%SUMO_HOME%\bin;%SUMO_HOME%\tools
```

### macOS/Linux:
```bash
export SUMO_HOME="/usr/local/share/sumo"  # Adjust path as needed
export PATH="$PATH:$SUMO_HOME/bin:$SUMO_HOME/tools"
export PYTHONPATH="$PYTHONPATH:$SUMO_HOME/tools"
```

Add these lines to your `.bashrc`, `.zshrc`, or equivalent shell configuration file.

## Step 4: Update SUMO Paths in Code

Edit `functions.py` and update the following lines:

```python
# Line 200, 258, 497, 522 - Update these paths:
sumoBinary = "PATH_YOUR_SUMO"  # Change to: "sumo"
sumoBinary = "PATH_YOUR_SUMO-GUI"  # Change to: "sumo-gui"
```

Replace with:
```python
sumoBinary = "sumo"  # For command line version
sumoBinary = "sumo-gui"  # For GUI version
```

## Step 5: Set up Gurobi (Optional but Recommended)

1. Register for a free academic license at: https://www.gurobi.com/academia/academic-program-and-licenses/
2. Download and install Gurobi
3. Activate your license following Gurobi's instructions

## Step 6: Verify Installation

Run this test script to verify everything is working:

```python
# test_setup.py
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

try:
    import traci
    print("✓ SUMO/TraCI imported successfully")
except ImportError:
    print("✗ SUMO/TraCI import failed - check SUMO installation and PYTHONPATH")

try:
    import pyqubo
    print("✓ PyQUBO imported successfully")
except ImportError:
    print("✗ PyQUBO import failed")

try:
    import gurobipy
    print("✓ Gurobi imported successfully")
except ImportError:
    print("✗ Gurobi import failed - install Gurobi and license")

try:
    import openjij
    print("✓ OpenJij imported successfully")
except ImportError:
    print("✗ OpenJij import failed")

try:
    import optuna
    print("✓ Optuna imported successfully")
except ImportError:
    print("✗ Optuna import failed")

print("\nCore libraries:")
print(f"✓ NumPy {np.__version__}")
print(f"✓ Pandas {pd.__version__}")
print("✓ Matplotlib and Seaborn available")
```

## Step 7: Run the Project

1. Start with the preprocessing notebook:
```bash
jupyter notebook preprocessing.ipynb
```

2. Run parameter search (optional):
```bash
jupyter notebook parameter_search.ipynb
```

3. Run the main optimization:
```bash
jupyter notebook main.ipynb
```

4. View results:
```bash
jupyter notebook show_result.ipynb
```

## Troubleshooting

### Common Issues:

1. **SUMO not found**: Ensure SUMO_HOME is set correctly and SUMO is in your PATH
2. **TraCI import error**: Add SUMO/tools to PYTHONPATH
3. **Gurobi license error**: Activate your Gurobi license
4. **Permission errors**: Run with appropriate privileges or use virtual environment

### Getting Help:

- SUMO documentation: https://sumo.dlr.de/docs/
- Gurobi documentation: https://www.gurobi.com/documentation/
- PyQUBO documentation: https://pyqubo.readthedocs.io/

## Project Structure

```
Traffic-Signal-Optimization-main/
├── requirements.txt          # Python dependencies
├── setup_guide.md           # This setup guide
├── functions.py             # Core optimization functions
├── main.ipynb              # Main optimization notebook
├── parameter_search.ipynb   # Parameter optimization
├── preprocessing.ipynb      # Data preprocessing
├── show_result.ipynb       # Results visualization
├── dataset/                # SUMO simulation data
│   ├── data.net.xml        # Network definition
│   ├── data.rou.xml        # Route definition
│   └── data.sumocfg        # SUMO configuration
└── *.pickle                # Saved results and parameters
```
