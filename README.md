# Traffic Signal Optimization

This project implements quantum and classical optimization algorithms for traffic signal control, as described in the paper available on [arXiv](https://arxiv.org/abs/2308.14462).

## Overview

The project uses SUMO (Simulation of Urban Mobility) for traffic simulation and implements various optimization approaches including:
- Quantum annealing with PyQUBO
- Classical optimization with Gurobi
- Simulated annealing with OpenJij and Neal
- Hyperparameter optimization with Optuna

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Install SUMO:**
   - Follow the [SUMO installation guide](https://sumo.dlr.de/docs/Installing/index.html)
   - Or see our detailed [setup guide](setup_guide.md)

3. **Configure SUMO paths:**
   ```bash
   python update_sumo_paths.py
   ```

4. **Test your setup:**
   ```bash
   python test_setup.py
   ```

5. **Run the project:**
   ```bash
   jupyter notebook main.ipynb
   ```

## Project Structure

```
├── requirements.txt          # Python dependencies
├── setup_guide.md           # Detailed setup instructions
├── test_setup.py            # Setup verification script
├── update_sumo_paths.py     # SUMO path configuration script
├── config_template.py       # Configuration template
├── functions.py             # Core optimization functions
├── main.ipynb              # Main optimization notebook
├── parameter_search.ipynb   # Parameter optimization
├── preprocessing.ipynb      # Data preprocessing
├── show_result.ipynb       # Results visualization
├── dataset/                # SUMO simulation data
│   ├── data.net.xml        # Network definition
│   ├── data.rou.xml        # Route definition
│   └── data.sumocfg        # SUMO configuration
└── *.pickle                # Saved results and parameters
```

## Requirements

- Python 3.7+
- SUMO traffic simulator
- Gurobi optimizer (free academic license available)
- See `requirements.txt` for complete Python dependencies

## Usage

1. **Preprocessing:** Run `preprocessing.ipynb` to prepare the traffic network data
2. **Parameter Search:** (Optional) Run `parameter_search.ipynb` to find optimal parameters
3. **Main Optimization:** Run `main.ipynb` to execute the traffic signal optimization
4. **Results:** View results in `show_result.ipynb`

## Troubleshooting

If you encounter issues:
1. Run `python test_setup.py` to verify your installation
2. Check the [setup guide](setup_guide.md) for detailed instructions
3. Ensure SUMO is properly installed and in your PATH
4. Verify Gurobi license is activated

## Citation

If you use this code in your research, please cite:

```bibtex
@article{traffic_signal_optimization_2023,
  title={Traffic Signal Optimization},
  author={[Authors]},
  journal={arXiv preprint arXiv:2308.14462},
  year={2023}
}
```

## License

[Add your license information here]

